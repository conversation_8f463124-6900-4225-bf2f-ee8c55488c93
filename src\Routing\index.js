import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er, HashRouter, Navigate, Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import Login from '../Pages/Login';
import httpclient from '../Utils';
import { useParams } from 'react-router-dom';
import Home from '../Pages/Home';
import MiniDrawer from '../Components/Drawer';
import Error404 from '../Components/Error404';
import MuiAlert from "@mui/material/Alert";
import {
    Box,
    Snackbar,
} from "@mui/material";

import Users from '../Pages/Users';
import Roles from '../Pages/Roles';
import useTokenRefresh from '../Hooks/useTokenRefresh';
import Projects from '../Pages/Projects/ListProjects';
import Footer from '../Components/Footer';
import HelmetMetaData from '../Components/HelmetMetaData';

import LoyaltyRules from '../Pages/LoyaltyRules';
import PushNotification from '../Pages/PushNotification';

import Customers from '../../src/Pages/Shopify/Customers';
import Orders from '../../src/Pages/Shopify/Orders';
import Products from '../../src/Pages/Shopify/Products';

import KlaviyoCustomers from '../Pages/Klaviyo/Customers';
import LoyaltyCustomers from '../Pages/Klaviyo/LoyaltyCustomers';

import LightSpeedCustomers from '../Pages/Lightspeed/Customers';
import LightSpeedProducts from '../Pages/Lightspeed/Products';
import LightSpeedOrders from '../Pages/Lightspeed/Orders';

import LoginLogs from '../Pages/AuditLogs/LoginLogs';
import ActionLogs from '../Pages/AuditLogs/ActionLogs';
import LoginFailedLogs from '../Pages/AuditLogs/LoginFailedLogs';

import WorkflowList from '../Pages/Projects/ModuleWorkFlow';
import ModuleWorkflow from '../Pages/Projects/ModuleWorkFlow';
import ProjectIntegration from '../Pages/Projects/ProjectIntegration';
import ListProjects from '../Pages/Projects/ListProjects';
import AssociatedDeveloper from '../Pages/Projects/AssociatedDeveloper';

import DynamicItems from '../Pages/Dynamic365/Items';
import DynamicCustomers from '../Pages/Dynamic365/Customers';
import DynamicDimensions from '../Pages/Dynamic365/Dimensions';

import NetSuiteProducts from '../Pages/NetSuite/Products';
import NetSuiteCustomers from '../Pages/NetSuite/Customers';
import NetSuiteLocations from '../Pages/NetSuite/Locations';
import NetSuiteSuppliers from '../Pages/NetSuite/Suppliers';

import Countries from '../Pages/PosCustomization/ListCountries';
import ImportCountriesPostCode from '../Pages/PosCustomization/ImportCountriesPostCode';
import PostCode from '../Pages/PosCustomization/ListPostCodes';
import Fog from '../Pages/PosCustomization/ListFog';

import ErplyPurchaseOrder from '../Pages/Erply/PurchaseOrder';
import ErplyInventoryRegistration from '../Pages/Erply/InventoryRegistration';
import ErplyInventoryWriteOff from '../Pages/Erply/InventoryWriteOff';
import ErplyProducts from '../Pages/Erply/Products';
import ErplyCustomers from '../Pages/Erply/Customers';
import ErplyOrders from '../Pages/Erply/Orders';
import ErplySuppliers from '../Pages/Erply/Suppliers';

import TransactionReport from '../Pages/Reports/TransactionReport';
import LoyaltyCustomersByStoreReport from '../Pages/Reports/LoyaltyCustomersByStoreReport';
import SalesReport from '../Pages/Reports/SalesReport';
import CustomerReport from '../Pages/Reports/CustomerReport';
import AutomatedPushNotification from '../Pages/AutomatedNotification';
import AutomatedPushNotificationHistory from '../Pages/AutomatedNotification/History';
import LoyalityPoints from '../Pages/LoyaltyPoints';
import CustomerDetails from '../Pages/LoyaltyPoints/CutomerDetails.jsx';
import CustomerPointDetails from '../Pages/LoyaltyPoints/CustomerPointDetails/index.js';

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const Routing = () => {
    const { getTokenRefreshed: refresh, overlay: overlay, open: tokenOpen, message: tokenMessage, messageState: tokenMessageState, setOpen: setTokenOpen } = useTokenRefresh();
    const [menuList, setMenuList] = useState([]);
    const [projects, setProjects] = useState([]);
    const [open, setOpen] = useState(false);
    const [overlayNew, setOverlayNew] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    var loginData = localStorage.getItem("user");
    var loginValue = JSON.parse(loginData);

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const ProtectedRoute = ({ children }) => {
        return localStorage.getItem("token") ? (
            <>
                <MiniDrawer children={children} menuList={menuList} projects={projects} handleMoveProject={handleMoveProject} />
            </>
        ) : (
            <Navigate to="/login"></Navigate>

        );
    };

    const componentMapping = {

        Shopify: Error404,
        Erply: Error404,
        Klaviyo: Error404,
        LightSpeed: Error404,
        Report: Error404,
        Users: Users,
        Roles: Roles,
        LoyaltyRules: LoyaltyRules,
        PushNotification: PushNotification,
        LoyalityPoints: LoyalityPoints,
        Projects: Error404,
        AuditLogs: Error404,
        ModuleWorkflow: ModuleWorkflow,
        Dynamic365: Error404,
        NetSuite: Error404,
        PosCustomization: Error404,
        AutomatedPushNotification: Error404,

    };

    const subComponentMapping = {

        Products: Products,
        Customers: Customers,
        Orders: Orders,
        ErplyProducts: ErplyProducts,
        ErplyCustomers: ErplyCustomers,
        ErplyOrders: ErplyOrders,
        ErplyPurchaseOrder: ErplyPurchaseOrder,
        ErplySuppliers: ErplySuppliers,
        ErplyInventoryRegistration: ErplyInventoryRegistration,
        ErplyInventoryWriteOff: ErplyInventoryWriteOff,
        KlaviyoCustomers: KlaviyoCustomers,
        KlaviyoLoyaltyCustomers: LoyaltyCustomers,
        LightSpeedProducts: LightSpeedProducts,
        LightSpeedCustomers: LightSpeedCustomers,
        LightSpeedOrders: LightSpeedOrders,
        SalesReport: SalesReport,
        CustomerReport: CustomerReport,
        TransactionReport: TransactionReport,
        LoyaltyCustomerByStore: LoyaltyCustomersByStoreReport,
        ListProject: ListProjects,
        AssociatedDeveloper: AssociatedDeveloper,
        ProjectIntegration: ProjectIntegration,
        LoginLogs: LoginLogs,
        ActionLogs: ActionLogs,
        LoginFailedLogs: LoginFailedLogs,
        DynamicItems: DynamicItems,
        DynamicCustomers: DynamicCustomers,
        DynamicDimensions: DynamicDimensions,
        NetSuiteProducts: NetSuiteProducts,
        NetSuiteCustomers: NetSuiteCustomers,
        NetSuiteLocations: NetSuiteLocations,
        NetSuiteSuppliers: NetSuiteSuppliers,
        ImportCountriesPostCode: ImportCountriesPostCode,
        Countries: Countries,
        PostCode: PostCode,
        Fog: Fog,
        AutomatedNotification: AutomatedPushNotification,
        NotificationHistory: AutomatedPushNotificationHistory

    };

    useEffect(() => {
        if (window.location.pathname !== "/login") {
            getAllMenus();
            if (loginValue && loginValue.is_project_shift_user === 1) {
                getAllProjects();
            }
        }
    }, []);

    function RouteChangeHandler() {
        const location = useLocation();

        useEffect(() => {
            if (location.pathname !== "/login" && loginValue) {
                getLogoutSession();
                const intervalId = setInterval(getLogoutSession, 30 * 1000);
                return () => clearInterval(intervalId);
            }
        }, [location.pathname]);
        return null;
    }

    const getLogoutSession = () => {

        httpclient
            .get(`project-shift-status?project_id=${loginValue.project_id}`)
            .then(({ data }) => {
                if (data.status === 1) {
                    sessionStorage.setItem("project_id", loginValue.project_id);
                    setOverlayNew(true);
                } else {
                    setOverlayNew(false);
                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOverlayNew(false);
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setOverlayNew(false);
                    setMessage(errorMessages);
                    setMessageState("error");

                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setOverlayNew(false);
                    setMessage(errorMessages);
                    setMessageState("error");


                } else {
                    setOpen(true);
                    setOverlayNew(false);
                    setMessage(err.response.data.message);
                    setMessageState("error");

                }
            })
    };


    const getAllMenus = () => {
        httpclient
            .get(`menu-list`)
            .then(({ data }) => {
                if (data.status === 200) {
                    setMenuList(data.data);
                } else {
                    setOpen(true);
                    setMessage(data.message);
                    setMessageState("error");

                }
            }).catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");

                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");


                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");

                }
            })
    };

    const getAllProjects = () => {
        httpclient.get(`projects?shift_project_list=1`).then(({ data }) => {
            if (data.status === 200) {
                setProjects(data.data);

            } else {
                setOpen(true);
                setMessage(data.message);
                setMessageState("error");

            }

        }).catch((err) => {
            if (err.response.status === 401) {
                refresh();
                setOpen(tokenOpen);
                setMessage(tokenMessage);
                setMessageState("error");
            } else if (err.response.status === 422) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");
            } else if (err.response.status === 400) {
                const errorMessages = Object.values(err.response.data.errors).flat();
                setOpen(true);
                setMessage(errorMessages);
                setMessageState("error");

            } else {
                setOpen(true);
                setMessage(err.response.data.message);
                setMessageState("error");
            }
        })

    };

    const handleMoveProject = (project) => {
        httpclient
            .post(`shift-project`, {
                project_id: project,
            })
            .then(({ data }) => {
                if (data.status === 200) {
                    setOpen(true);
                    setMessageState("success");
                    setMessage(data.message);
                    let user = JSON.parse(localStorage.getItem("user"));
                    user.company_name = data.data.company_name;
                    user.project_id = data.data.project_id;
                    localStorage.setItem("user", JSON.stringify(user));
                    setTimeout(() => {
                        window.location.href = "/";
                    }, 500);
                } else {
                    setOpen(true);
                    setMessageState("error");
                    setMessage(data.message);
                }
            })
            .catch((err) => {
                if (err.response.status === 401) {
                    refresh();
                    setOpen(tokenOpen);
                    setMessage(tokenMessage);
                    setMessageState("error");
                } else if (err.response.status === 422) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");
                } else if (err.response.status === 400) {
                    const errorMessages = Object.values(err.response.data.errors).flat();
                    setOpen(true);
                    setMessage(errorMessages);
                    setMessageState("error");

                } else {
                    setOpen(true);
                    setMessage(err.response.data.message);
                    setMessageState("error");
                }
            });

    };


    return (
        <div>
            <HelmetMetaData
                title={loginValue && loginValue.company_name}
                description={
                    "SyncCare"
                }
            />
            <BrowserRouter>
                <RouteChangeHandler />
                <Routes>

                    <Route path="/login" element={<Login />} />

                    <Route path="/" element={<ProtectedRoute><Home open={tokenOpen} overlay={overlay} overlayNew={overlayNew} setOpen={setTokenOpen} messageState={tokenMessageState} message={tokenMessage} /></ProtectedRoute>} />


                    {menuList.length > 0 && menuList.map((menu) => {
                        const Component = componentMapping[menu.component];
                        if (!Component) {
                            return (
                                <Route
                                    key={menu.id}
                                    path={menu.path}
                                    element={<ProtectedRoute><Error404 overlay={overlay} overlayNew={overlayNew} /></ProtectedRoute>}
                                />
                            );
                        }

                        return (
                            <>
                                <Route
                                    key={menu.id}
                                    path={menu.path}
                                    element={<ProtectedRoute><Component permissions={menu.permissions} getAllProjects={getAllProjects} overlayNew={overlayNew} /></ProtectedRoute>}
                                />
                                {menu?.sub_menu?.length > 0 && menu?.sub_menu?.map((subMenu) => {
                                    const SubComponent = subComponentMapping[subMenu.component];
                                    if (!SubComponent) {
                                        return (
                                            <Route
                                                key={subMenu.id}
                                                path={subMenu.path}
                                                element={<ProtectedRoute><Error404 overlay={overlay} overlayNew={overlayNew} /></ProtectedRoute>}
                                            />
                                        );
                                    }

                                    return (
                                        <Route
                                            key={subMenu.id}
                                            path={subMenu.path}
                                            element={<ProtectedRoute><SubComponent permissions={subMenu.permissions} getAllProjects={getAllProjects} request_name={subMenu.request_name} overlayNew={overlayNew} loginValue={loginValue} /></ProtectedRoute>}
                                        />
                                    );
                                })}
                            </>
                        );
                    })}
                    <Route path="*" element={<ProtectedRoute><Error404 /></ProtectedRoute>} />
                    <Route path="/customer-details" element={<ProtectedRoute><CustomerDetails/></ProtectedRoute>} />
                    <Route path="/customer-point-details/:id" element={<ProtectedRoute><CustomerPointDetails/></ProtectedRoute>} />
                </Routes>
            </BrowserRouter>
            <Snackbar
                open={open}
                autoHideDuration={6000}
                onClose={handleClose}
                anchorOrigin={{ vertical: "top", horizontal: "center" }}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
}

export default Routing;
