import { Box, Button, Grid, Snackbar, styled, Card, Collapse, TextField, InputLabel, FormControl, Select, MenuItem, Chip, Autocomplete, Checkbox } from '@mui/material';
import React, { useEffect, useState, useCallback } from 'react'
import TableComponent from '../../Components/TableComponent';

import { Add, <PERSON><PERSON><PERSON><PERSON>, FilterList, Close } from '@mui/icons-material';
import httpclient from '../../Utils';
import MuiAlert from "@mui/material/Alert";
import EditAutomatedNotification from '../../Components/EditAutomatedNotification';
import DeleteDialog from '../../Components/DeleteDialog';
import moment from 'moment';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';



// Add styled component for filter chips
const FilteredBox = styled(Box)(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    gap: "10px",
    marginBottom: "15px",
    "& p": {
        display: "flex",
        alignItems: "center",
        background: theme.palette.primary.light,
        padding: "5px 10px",
        borderRadius: "5px",
        margin: 0,
        "& svg": {
            marginLeft: "5px",
            cursor: "pointer",
        },
    },
}));

const Header = styled("div")(({ theme }) => ({
    "& h1": {
        color: theme.palette.primary.dark,
        margin: "0",
    },
}));

const AddButton = styled(Button)(({ theme }) => ({
    marginLeft: "10px",
    "& svg": {
        fontSize: "15px",
    },
}));

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const checkboxIcon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkboxCheckedIcon = <CheckBoxIcon fontSize="small" />;

const AutomatedPushNotification = () => {
    const [rows, setRows] = useState([]);
    const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
    const [openEditDialog, setOpenEditDialog] = useState(false);
    const [viewDetails, setViewDetails] = useState({});
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("success");

    const [loading, setLoading] = useState(false);
    const [rowLoading, setRowLoading] = useState({});
    const [isAddNew, setIsAddNew] = useState(false);
    const [notificationTypes, setNotificationTypes] = useState([]);
    const [showActiveFilters, setShowActiveFilters] = useState(false);

    // Helper function to migrate old filter format to new format
    const migrateFilterFormat = (savedFilters) => {
        if (!savedFilters) return { notifyTypeIds: [], status: '' };

        const parsed = JSON.parse(savedFilters);

        // If old format with notifyTypeId (single), convert to notifyTypeIds (array)
        if (parsed.notifyTypeId && !parsed.notifyTypeIds) {
            return {
                notifyTypeIds: parsed.notifyTypeId ? [parsed.notifyTypeId] : [],
                status: parsed.status || ''
            };
        }

        // Ensure notifyTypeIds is always an array
        return {
            notifyTypeIds: parsed.notifyTypeIds || [],
            status: parsed.status || ''
        };
    };

    // Add filters state - similar to History component
    const [filters, setFilters] = useState(() => {
        const savedFilters = localStorage.getItem('automated_notification_filter');
        return migrateFilterFormat(savedFilters);
    });

    // Add activeFilters state to track applied filters
    const [activeFilters, setActiveFilters] = useState(() => {
        const savedFilters = localStorage.getItem('automated_notification_filter');
        return migrateFilterFormat(savedFilters);
    });

    // Add filter UI state
    const [filterOpen, setFilterOpen] = useState(false);

    // Add pagination states
    const [page, setPage] = useState(0);
    const [total, setTotal] = useState(0);
    const [from, setFrom] = useState(0);
    const [to, setTo] = useState(0);

    const configRowPerPage = localStorage.getItem("configRowPerPage");
    const [rowsPerPage, setRowsPerPage] = useState(
        configRowPerPage ? parseInt(configRowPerPage) : 20
    );

    // Add sorting state for TableComponent
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");

    const userData = JSON.parse(localStorage.getItem("user"));

    const columns = [
        { id: "title", name: "Title" },
        { id: "notify_type", name: "Notification Type" },
        { id: "message", name: "Message" },
        { id: "status", name: "Status" },
        { id: "last_pushed_date", name: "Last Pushed Date" },
        { id: "created_at", name: "Created At" },
        { id: "actions", name: "Actions" },
    ];



    useEffect(() => {
        // Check if there are any active filters in localStorage
        const savedFilters = localStorage.getItem('automated_notification_filter');
        if (savedFilters) {
            const migratedFilters = migrateFilterFormat(savedFilters);
            // Show filter chips if any filter is active
            if (migratedFilters.notifyTypeIds?.length > 0 || migratedFilters.status) {
                setShowActiveFilters(true);
                setActiveFilters(migratedFilters);
                setFilters(migratedFilters); // Also update the current filters
                // Save the migrated format back to localStorage
                localStorage.setItem('automated_notification_filter', JSON.stringify(migratedFilters));
            }
        }

        fetchNotificationTypes();
    }, []); // Run only once on component mount


    // Fetch notification types for the dropdown
    const fetchNotificationTypes = async () => {
        try {
            console.log('Fetching notification types...'); // Debug log
            const response = await httpclient.get("request-response?requestName=lightspeed/notification/types");
            console.log('Notification types response:', response.data); // Debug log
            if (response.data?.data) {
                setNotificationTypes(response.data.data);
                console.log('Notification types set:', response.data.data); // Debug log
            }
        } catch (error) {
            console.error("Failed to fetch notification types", error);
        }
    };

    const getNotificationList = useCallback(async (page = 0, rowsPerPage = 20) => {
        try {
            setLoading(true);
            let url = `request-response?requestName=lightspeed/automated/notifications&page=${page + 1}&per_page=${rowsPerPage}`;

            // Use activeFilters (from localStorage) instead of current filters
            const params = [];

            if (activeFilters.notifyTypeIds?.length > 0) {
                activeFilters.notifyTypeIds.forEach((typeId, index) => {
                    params.push(`filters[notify_type_id][$in][${index}]=${encodeURIComponent(typeId)}`);
                });
            }

            if (activeFilters.status) {
                params.push(`filters[status][$eq]=${activeFilters.status}`);
            }

            // Add sorting parameters (simple single column sorting)
            if (currentColumn) {
                params.push(`sort[0]=${currentColumn}:${direction ? "asc" : "desc"}`);
            }

            if (params.length) url += `&${params.join('&')}`;

            console.log('API URL with filters and sorting:', url); // Debug log to check the URL

            const response = await httpclient.get(url); // Use .get() method explicitly

            console.log('API Response:', response.data); // Debug log to check response

            const apiData = response.data?.data || [];
            const meta = response.data?.meta || {};

            const formattedData = apiData.map((item) => ({
                id: item.id,
                title: item.title,
                notify_type: item.notify_type?.name || item.notify_type, // Handle nested notify_type object
                notify_type_id: item.notify_type_id,
                message: item.message,
                status: item.status,
                last_pushed: item.last_pushed_date,
                created_at: moment(item.created_at).format("YYYY-MM-DD hh:mm A"),
                actions: "Edit | Delete"
            }));

            setRows(formattedData);
            setTotal(meta.total || formattedData.length);
            setFrom(meta.from || 1);
            setTo(meta.to || formattedData.length);
            setPage(meta.current_page ? meta.current_page - 1 : 0);
        } catch (error) {
            console.error("Failed to fetch notifications", error);
            setOpen(true);
            setMessage("Failed to fetch notifications");
            setMessageState("error");
        } finally {
            setLoading(false);
        }
    }, [activeFilters, currentColumn, direction]);

    // Separate useEffect to fetch data after activeFilters are set
    useEffect(() => {
        getNotificationList();
    }, [activeFilters, getNotificationList]); // Run when activeFilters change

    // Handle sorting (simple single column sorting like Users component)
    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        setLoading(true);
        getNotificationList(page, rowsPerPage);
    };

    // Filter functions
    const handleFilterOpen = () => {
        setFilterOpen(!filterOpen);
    };

    const handleFilter = () => {
        console.log('Applying filters:', filters); // Debug log

        // Save to localStorage and update activeFilters
        localStorage.setItem('automated_notification_filter', JSON.stringify(filters));
        setActiveFilters({ ...filters });

        // Show chips only if there are active filters
        setShowActiveFilters(
            filters.notifyTypeIds?.length > 0 ||
            filters.status
        );

        // Reset page to 0 when filtering
        setPage(0);
        getNotificationList(0, rowsPerPage);
    };

    const handleRemoveFilter = (key, typeId = null) => {
        console.log('Removing filter:', key, typeId); // Debug log

        // Update both current filters and active filters
        const updated = { ...filters };

        if (key === 'notifyTypeIds' && typeId) {
            // Remove specific notification type
            updated.notifyTypeIds = (updated.notifyTypeIds || []).filter(id => id !== typeId);
        } else if (key === 'notifyTypeIds') {
            // Remove all notification types
            updated.notifyTypeIds = [];
        } else {
            // Remove other filters (like status)
            updated[key] = '';
        }

        setFilters(updated);
        setActiveFilters(updated);
        localStorage.setItem('automated_notification_filter', JSON.stringify(updated));

        // Hide chips if all filters cleared
        if ((updated.notifyTypeIds || []).length === 0 && !updated.status) {
            setShowActiveFilters(false);
        }

        // Reset page to 0 when removing filters
        setPage(0);
        getNotificationList(0, rowsPerPage);
    };

    // Add pagination handlers
    const handleChangePage = (e, newPage) => {
        setPage(newPage);
        getNotificationList(newPage, rowsPerPage);
    };

    const handleChangeRowsPerPage = (event) => {
        const newRowsPerPage = parseInt(event.target.value, 10);
        setRowsPerPage(newRowsPerPage);
        setPage(0);
        localStorage.setItem("configRowPerPage", newRowsPerPage);
        getNotificationList(0, newRowsPerPage);
    };

    const handleAddNew = () => {
        setIsAddNew(true);
        setOpenEditDialog(true);
        setViewDetails({});
    };

    const handleEdit = (row) => {
        setOpenEditDialog(true);
        console.log(row)
        setViewDetails(row);
    };

    const sendEdit = (call, formData) => {
        if (call.open === false) {
            setOpenEditDialog(false);
            setViewDetails({});
            setIsAddNew(false);
        }
        if (call.success === true) {
            if (isAddNew) {
                // Create new notification
                httpclient
                    .post(`request-response?requestName=lightspeed/automated/notification/create`, formData)
                    .then((response) => {
                        if (response.status === 200) {
                            getNotificationList();
                            setOpen(true);
                            setMessageState("success");
                            setMessage(response.data?.message || "Added successfully");
                            setViewDetails({});
                            setOpenEditDialog(false);
                        } else {
                            setOpen(true);
                            setMessage(response.data?.message || "An error occurred");
                            setMessageState("error");
                            setLoading(false);
                        }
                    }).catch((err) => {
                        setOpen(true);
                        setMessage(err?.response?.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                    }).finally(() => {
                        setOpenEditDialog(false);
                        setLoading(false);
                    });
            } else if (viewDetails.id) {
                // Update existing notification
                httpclient
                    .put(`request-response?requestName=lightspeed/automated/notification/update/${viewDetails.id}`, formData)
                    .then((response) => {
                        if (response.status === 200) {
                            getNotificationList();
                            setOpen(true);
                            setMessageState("success");
                            setMessage(response.data?.message || "Updated successfully");
                            setViewDetails({});
                            setOpenEditDialog(false);
                        } else {
                            setOpen(true);
                            setMessage(response.data?.message || "An error occurred");
                            setMessageState("error");
                            setLoading(false);
                        }
                    }).catch((err) => {
                        setOpen(true);
                        setMessage(err?.response?.data?.message || "An error occurred");
                        setMessageState("error");
                        setLoading(false);
                    });
            }
        }
    };

    const handleDelete = (row) => {
        setOpenDeleteDialog(true);
        setViewDetails(row);
    };

    const sendDelete = (call) => {
        if (call.open === false) {
            setOpenDeleteDialog(false);
            setViewDetails({});
        }
        if (call.success === true) {
            httpclient
                .delete(`request-response?requestName=lightspeed/delete/automated-notification/${viewDetails.id}`, { data: { deleted_by: userData?.user_name } })
                .then((response) => {
                    if (response.status === 200) {
                        setOpen(true);
                        setMessageState("success");
                        setMessage(response?.data?.message);
                        setOpenDeleteDialog(false);
                        setViewDetails({});
                        getNotificationList();
                    } else {
                        setOpen(true);
                        setMessage(response?.data?.message);
                        setMessageState("error");
                        setLoading(false);
                    }
                }).catch((err) => {
                    setOpen(true);
                    setMessage(err?.response?.data?.message || "An error occurred");
                    setMessageState("error");
                    setLoading(false);
                });
        }
    };

    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    const updateRowStatus = async (row) => {
        console.log(row)
        try {
            setRowLoading((prev) => ({ ...prev, [row.id]: true }));

            // Toggle the status (assuming status is 0 or 1)
            const newStatus = row.status === 1 ? 0 : 1;

            const payload = {
                status: newStatus,
                notify_type_id: row.notify_type_id || '',
                title: row.title || '',
                message: row.message || '',
                updated_by: userData?.user_name || '',
            };

            // Make API call to update status
            const response = await httpclient.put(
                `request-response?requestName=lightspeed/automated/notification/update/${row.id}`,
                payload
            );

            if (response.status === 200) {
                // Show success message
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Status updated successfully");

                // Refresh the notification list
                await getNotificationList();
            } else {
                // Show error message
                setOpen(true);
                setMessage(response.data?.message || "Failed to update status");
                setMessageState("error");
            }
        } catch (error) {
            // Handle error
            setOpen(true);
            setMessage(error?.response?.data?.message || "An error occurred");
            setMessageState("error");
        } finally {
            setRowLoading((prev) => ({ ...prev, [row.id]: false }));
        }
    };

    const currentChange = (value, row) => {
        if (value === "allow_update") {
            handleEdit(row);
        }
        if (value === "allow_delete") {
            handleDelete(row);
        }
    };



    return (
        <Box>
            <Grid container spacing={2} alignItems="center" justifyContent="space-between">
                <Grid item xs={12} md={8}>
                    <Header>
                        <h1>List Automated Push Notification</h1>
                    </Header>
                </Grid>

                <Grid item xs={12} md={4}>
                    <Box
                        display="flex"
                        flexDirection={{ xs: "column", sm: "row" }}
                        justifyContent={{ xs: "flex-start", md: "flex-end" }}
                        alignItems={{ xs: "stretch", sm: "center" }}
                        gap={1}
                    >
                        <Button
                            color="primary"
                            variant="contained"
                            onClick={handleFilterOpen}
                            fullWidth={{ xs: true, sm: false }}
                        >
                            Filter <FilterList sx={{ ml: 1 }} fontSize="small" />
                        </Button>

                        <AddButton
                            color="primary"
                            variant="contained"
                            onClick={handleAddNew}
                            fullWidth={{ xs: true, sm: false }}
                        >
                            <Add sx={{ mr: 1 }} fontSize="small" /> Add Notification
                        </AddButton>
                    </Box>
                </Grid>

                {/* Filter Section */}
                <Grid item xs={12}>
                    <Collapse in={filterOpen}>
                        <Card>
                            <Box p={4}>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} md={6}>
                                        <InputLabel>Notification Type</InputLabel>
                                        <Autocomplete
                                            options={notificationTypes}
                                            multiple
                                            disableCloseOnSelect
                                            getOptionLabel={(option) => option.name}
                                            renderOption={(props, option, { selected }) => (
                                                <li {...props}>
                                                    <Checkbox icon={checkboxIcon} checkedIcon={checkboxCheckedIcon} checked={selected} />
                                                    {option.name}
                                                </li>
                                            )}
                                            value={notificationTypes.filter(type => filters.notifyTypeIds?.includes(type.id)) || []}
                                            onChange={(e, newValue) => setFilters({ ...filters, notifyTypeIds: newValue.map(type => type.id) })}
                                            renderInput={(params) => <TextField {...params} variant="outlined" />}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                        <InputLabel>Status</InputLabel>
                                        <FormControl fullWidth>
                                            <Select
                                                name="status"
                                                value={filters.status || ''}
                                                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                                            >
                                                <MenuItem value="">Select</MenuItem>
                                                <MenuItem value="1">Active</MenuItem>
                                                <MenuItem value="0">Inactive</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Box textAlign="right">
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleFilter}
                                            >
                                                Filter <ArrowForward style={{ marginLeft: "5px" }} fontSize="small" />
                                            </Button>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                        </Card>
                    </Collapse>
                </Grid>

                {/* Filter Chips */}
                {showActiveFilters && (
                    <Grid item xs={12}>
                        <Box display="flex" flexWrap="wrap" gap={1} mt={2} mb={2}>
                            {activeFilters.notifyTypeIds?.length > 0 && activeFilters.notifyTypeIds.map(typeId => {
                                const type = notificationTypes.find(t => t.id === parseInt(typeId));
                                return (
                                    <Chip
                                        key={typeId}
                                        label={`Notification Type: ${type?.name || typeId}`}
                                        onDelete={() => handleRemoveFilter('notifyTypeIds', typeId)}
                                    />
                                );
                            })}
                            {activeFilters.status && (
                                <Chip
                                    label={`Status: ${activeFilters.status === "1" ? "Active" : "Inactive"}`}
                                    onDelete={() => handleRemoveFilter('status')}
                                />
                            )}
                        </Box>
                    </Grid>
                )}

                <Grid item xs={12}>
                    <TableComponent
                        name={"Notification"}
                        columns={columns}
                        rows={rows}
                        sort={true}
                        handleSort={handleSort}
                        direction={direction}
                        currentColumn={currentColumn}
                        loading={loading}
                        rowLoading={rowLoading}
                        setRowLoading={setRowLoading}
                        updateRowStatus={updateRowStatus}
                        props={{
                            permissions: [
                                { name: "allow_update", status: 1 },
                                { name: "allow_delete", status: 1 }
                            ]
                        }}
                        options={[
                            { name: "allow_update", status: 1 },
                            { name: "allow_delete", status: 1 }
                        ]}
                        currentChange={currentChange}
                        footer={true}
                        page={page}
                        rowsPerPage={rowsPerPage}
                        total={total}
                        fromTable={from}
                        toTable={to}
                        handleChangePage={handleChangePage}
                        handleChangeRowsPerPage={handleChangeRowsPerPage}
                    />
                </Grid>
            </Grid>

            {/* Existing dialogs and snackbar */}
            {openDeleteDialog && <DeleteDialog name={"Notification"} viewDetails={viewDetails} sendDelete={sendDelete} />}

            {openEditDialog && (
                <EditAutomatedNotification
                    viewDetails={viewDetails}
                    sendEdit={sendEdit}
                    isAddNew={isAddNew}
                />
            )}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleClose}
            >
                <Alert
                    onClose={handleClose}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </Box>
    )
}

export default AutomatedPushNotification
