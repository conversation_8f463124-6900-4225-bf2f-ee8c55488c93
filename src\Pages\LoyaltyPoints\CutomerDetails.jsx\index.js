import React, { useState } from 'react';
import {
    Box,
    Typography,
    Button,
    Grid,
    Divider,
    Paper,
    Snackbar,
} from '@mui/material';
import MuiAlert from "@mui/material/Alert";
import { useLocation, useNavigate } from 'react-router-dom';
import httpclient from '../../../Utils';

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

export default function CustomerDetails() {
    const location = useLocation();
    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState('');
    const [messageState, setMessageState] = useState('success');
    const [isLoading, setIsLoading] = useState(false);

    const { customerData } = location.state || {};

    const [receiptData, setReceiptData] = useState({
        receiptNumber: '',
        receiptAmount: '',
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setReceiptData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleApplyPoint = async (e) => {
        try {
            setIsLoading(true);
            e.preventDefault();
            const payload = {
                synccare_internal_id: customerData?.customer?.id,
                receipt_number: receiptData.receiptNumber,
                receipt_value: receiptData.receiptAmount,
            };

            const response = await httpclient.post(`request-response?requestName=lightspeed/apply-point`, payload);

            if (response.status === 200) {
                setIsLoading(false);
                setOpen(true);
                setMessageState("success");
                setMessage(response.data?.message || "Added successfully");
                setReceiptData({
                    receiptNumber: '',
                    receiptAmount: '',
                });
            } else {
                setOpen(true);
                setMessageState("error");
                setMessage(response?.data?.message || "An error occurred");
            }
        } catch (error) {
            setIsLoading(false);
            setOpen(true);
            setMessageState("error");
            const errMsg = error?.response?.data?.message || error?.message || "An error occurred";
            setMessage(errMsg);
        }
        finally {
            setIsLoading(false);
        }
    };


    const fetchPointDetails = async () => {
        navigate(`/customer-point-details/${customerData?.customer?.id}`);
    };

    const customerDetails = [
        { label: 'Full Name', value: customerData?.customer?.full_name || '-' },
        { label: 'Mobile', value: customerData?.customer?.phone || '-' },   
        { label: 'Email', value: customerData?.customer?.email || '-' },
        { label: 'LiteCard Member ID', value: customerData?.liteCardMemberInfo?.external_id || '-' },
        { label: 'Lightspeed ID', value: customerData?.lightSpeedInfo?.external_id || '-' },
        { label: 'Current Points', value: customerData?.customer?.current_points || '0' },
        { label: 'Points Redeemed', value: customerData?.customer?.points_redeemed || '0' },
        { label: 'Last Point Updated', value: customerData?.customer?.last_point_update || '-' },
    ];

    return (
        <Box
            sx={{
                minHeight: '100vh',
                p: 2,
            }}
        >
            <Paper
                elevation={3}
                sx={{
                    width: '100%',
                    p: 4,
                    borderRadius: 2,
                }}
            >
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                    Customer Details
                </Typography>

                <Grid container spacing={2} mt={2}>
                    {customerDetails.map((item, index) => (
                        <Grid item xs={6} key={index}>
                            <Box display="flex" >
                                <Typography fontWeight="bold" >
                                    {item.label}:
                                </Typography>
                                <Typography fontWeight="500" sx={{ ml: 1 }}>{item.value}</Typography>
                            </Box>
                        </Grid>
                    ))}
                </Grid>

                <Box mt={8} display="flex" justifyContent="center" gap={2} >
                    <Button variant="contained" onClick={fetchPointDetails}>
                        Point Details
                    </Button>
                    <Button variant="outlined" color="inherit" onClick={() => navigate(-1)}>
                        Return Back
                    </Button>
                </Box>

                <Divider sx={{ my: 4 }} />

                <Typography variant="h6" fontWeight="bold" align="center" mb={2}>
                    Apply Loyalty Point By Receipt
                </Typography>

                <form onSubmit={handleApplyPoint}>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <Typography fontWeight="500" mb={0.5}>Receipt Number</Typography>
                            <input
                                style={{
                                    width: '100%',
                                    padding: '10px',
                                    border: '1px solid #ccc',
                                    borderRadius: '4px',
                                }}
                                type="text"
                                name="receiptNumber"
                                placeholder="Enter receipt number"
                                value={receiptData.receiptNumber}
                                onChange={handleChange}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <Typography fontWeight="500" mb={0.5}>Receipt Amount</Typography>
                            <input
                                style={{
                                    width: '100%',
                                    padding: '10px',
                                    border: '1px solid #ccc',
                                    borderRadius: '4px',
                                }}
                                type="text"
                                name="receiptAmount"
                                placeholder="Enter receipt amount"
                                value={receiptData.receiptAmount}
                                onChange={handleChange}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <Button
                                disabled={isLoading}
                                type="submit"
                                fullWidth
                                variant="contained"
                                sx={{
                                    textTransform: 'none',
                                    fontWeight: 'bold',
                                }}
                            >
                                {isLoading ? 'Applying...' : 'Apply Point'}
                            </Button>
                        </Grid>
                    </Grid>
                </form>
            </Paper>
            <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={() => setOpen(false)}>
                <Alert onClose={() => setOpen(false)} severity={messageState} sx={{ width: '100%' }}>{message}</Alert>
            </Snackbar>
        </Box>
    );
}
