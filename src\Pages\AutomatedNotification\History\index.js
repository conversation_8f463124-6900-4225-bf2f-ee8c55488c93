import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Card, Box, InputLabel, TextField, Button, Chip, Typography, Paper,
    Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Snackbar,
    TablePagination, Autocomplete, Checkbox,
    IconButton,
    Modal,
    Skeleton,
    TableSortLabel
} from '@mui/material';
import { ArrowFor<PERSON>, FilterList, Visibility } from '@mui/icons-material';
import MuiAlert from "@mui/material/Alert";
import httpclient from '../../../Utils';
import moment from 'moment';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

// Define sortable columns (excluding View Details)
const sortableColumns = [
    { id: 'title', label: 'Title', field: 'notification.title' },
    { id: 'store_id', label: 'Store', field: 'store_id.name' },
    { id: 'litecard_member_id', label: 'Litecard Member ID', field: 'litecard_member_id' },
    { id: 'fullname', label: 'Full Name', field: 'litecard_details.first_name' },
    { id: 'email', label: 'Email', field: 'email' },
    { id: 'last_sent', label: 'Sent Date', field: 'created_at' }
];

export default function AutomatedPushNotificationHistory() {
    const [filterOpen, setFilterOpen] = useState(false);
    const [filters, setFilters] = useState(() => {
        const savedFilters = localStorage.getItem('notificationFilters');
        return savedFilters ? JSON.parse(savedFilters) : { startDate: '', endDate: '', storeIds: [], litecard_member_id: '', email: '' };
    });
    const [activeFilters, setActiveFilters] = useState(() => {
        const savedFilters = localStorage.getItem('notificationFilters');
        return savedFilters ? JSON.parse(savedFilters) : { startDate: '', endDate: '', storeIds: [], litecard_member_id: '', email: '' };
    });
    const [showActiveFilters, setShowActiveFilters] = useState(false);
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState('');
    const [messageState, setMessageState] = useState('success');
    const [pagination, setPagination] = useState({ page: 0, rowsPerPage: 20, total: 0 });
    const [stores, setStores] = useState([]);
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedRow, setSelectedRow] = useState(null);

    // Sorting state
    const [direction, setDirection] = useState(false);
    const [currentColumn, setCurrentColumn] = useState("");

    useEffect(() => {
        // Check if there are any active filters in localStorage
        const savedFilters = localStorage.getItem('notificationFilters');
        if (savedFilters) {
            const parsedFilters = JSON.parse(savedFilters);
            // Show filter chips if any filter is active
            if (
                (parsedFilters.startDate && parsedFilters.endDate) || 
                parsedFilters.storeIds?.length > 0 || 
                parsedFilters.litecard_member_id || 
                parsedFilters.email
            ) {
                setShowActiveFilters(true);
                setActiveFilters(parsedFilters);
            }
        }
        
        fetchData(0, pagination.rowsPerPage);
    }, []);

    const fetchData = async (page = 0, rowsPerPage = 20, sortColumn = null, sortDirection = null) => {
        try {
            setLoading(true);
            let url = `request-response?requestName=lightspeed/notification/history&page=${page + 1}&per_page=${rowsPerPage}`;

            // Use activeFilters (from localStorage) instead of current filters
            const params = [];
            if (activeFilters.startDate && activeFilters.endDate) {
                params.push(`filters[created_at][$between][0]=${activeFilters.startDate}`);
                params.push(`filters[created_at][$between][1]=${activeFilters.endDate}`);
            }
            if (activeFilters.storeIds?.length) {
                activeFilters.storeIds.forEach((store, index) => {
                    params.push(`filters[store_id][$in][${index}]=${encodeURIComponent(store.id)}`);
                });
            }
            if (activeFilters.litecard_member_id) {
                params.push(`filters[litecard_member_id][$eq]=${activeFilters.litecard_member_id}`);
            }
            if (activeFilters.email) {
                params.push(`filters[email][$eq]=${activeFilters.email}`);
            }

            // Add sorting parameters
            if (sortColumn && sortDirection) {
                params.push(`sort[0]=${sortColumn}:${sortDirection}`);
            } else if (currentColumn) {
                params.push(`sort[0]=${currentColumn}:${direction ? "asc" : "desc"}`);
            }

            if (params.length) url += `&${params.join('&')}`;

            const res = await httpclient.get(url);
            if (res?.data?.responseCode === 200) {
                const apiData = res.data.data || [];
                const formatted = apiData.map((item, index) => ({
                    sn: (page * rowsPerPage) + index + 1,
                    title: item?.notification?.title || '-',
                    store_id: item?.store_id?.name || '-',
                    litecard_member_id: item?.litecard_member_id || '-',
                    fullname: item?.litecard_details?.first_name + ' ' + item?.litecard_details?.last_name || '-',
                    email: item?.email || '-',
                    message_sent: item?.notification?.message || "-",
                    last_sent: moment(item?.created_at).format("YYYY-MM-DD hh:mm A")
                }));
                setData(formatted);
                setStores(res.data.sites || []);
                setPagination(prev => ({ ...prev, total: res.data.meta.total, page, rowsPerPage }));
            } else {
                setData([]);
                setMessage('Failed to load data');
                setMessageState('error');
                setOpen(true);
            }
        } catch (err) {
            console.error(err);
            setMessage('Error fetching data');
            setMessageState('error');
            setOpen(true);
        } finally {
            setLoading(false);
        }
    };

    const handleSort = (column) => {
        setDirection((prevDirection) => !prevDirection);
        setCurrentColumn(column);
        const sortDirection = !direction ? "asc" : "desc";
        fetchData(pagination.page, pagination.rowsPerPage, column, sortDirection);
    };

    const handleFilter = () => {
        if (filters.startDate && !filters.endDate || !filters.startDate && filters.endDate) {
            setMessage('Please select both start and end dates');
            setMessageState('error');
            setOpen(true);
            return;
        }
        
        // Save to localStorage and update activeFilters
        localStorage.setItem('notificationFilters', JSON.stringify(filters));
        setActiveFilters({...filters});
        
        // Show chips only if there are active filters
        setShowActiveFilters(
            (filters.startDate && filters.endDate) ||
            filters.storeIds?.length > 0 ||
            filters.litecard_member_id ||
            filters.email
        );

        const sortDirection = currentColumn ? (direction ? "asc" : "desc") : null;
        fetchData(0, pagination.rowsPerPage, currentColumn, sortDirection);
    };

    const handleRemoveFilter = (key, storeId = null) => {
        // Update both current filters and active filters
        const updated = { ...filters };
        if (key === 'storeIds' && storeId) {
            updated.storeIds = updated.storeIds.filter(store => store.id !== storeId);
        } else if (key === 'storeIds') {
            updated.storeIds = [];
        } else if (key === 'dateRange') {
            updated.startDate = '';
            updated.endDate = '';
        } else {
            updated[key] = '';
        }

        setFilters(updated);
        setActiveFilters(updated);
        localStorage.setItem('notificationFilters', JSON.stringify(updated));
        
        // Hide chips if all filters cleared
        if (!updated.startDate && !updated.endDate && updated.storeIds.length === 0 && !updated.litecard_member_id && !updated.email) {
            setShowActiveFilters(false);
        }

        const sortDirection = currentColumn ? (direction ? "asc" : "desc") : null;
        fetchData(0, pagination.rowsPerPage, currentColumn, sortDirection);
    };

    const handleViewClick = (row) => {
        setSelectedRow(row);
        setModalOpen(true);
    };

    const handleModalClose = () => {
        setModalOpen(false);
        setSelectedRow(null);
    };

    return (
        <Box>
            <Grid container alignItems="center" justifyContent="space-between" mb={2}>
                <Grid item>
                    <Typography variant="h4" fontWeight="bold">Automated Push Notification History</Typography>
                </Grid>
                <Grid item>
                    <Button color="primary" variant="contained" onClick={() => setFilterOpen(!filterOpen)}>
                        Filter <FilterList sx={{ ml: 1 }} fontSize="small" />
                    </Button>
                </Grid>
            </Grid>

            <Collapse in={filterOpen}>
                <Card>
                    <Box p={3}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={4}>
                                <InputLabel>Store Name</InputLabel>
                                <Autocomplete
                                    options={stores}
                                    multiple
                                    disableCloseOnSelect
                                    getOptionLabel={(option) => option.name}
                                    renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                            <Checkbox icon={icon} checkedIcon={checkedIcon} checked={selected} />
                                            {option.name}
                                        </li>
                                    )}
                                    value={filters.storeIds}
                                    onChange={(e, newValue) => setFilters({ ...filters, storeIds: newValue })}
                                    renderInput={(params) => <TextField {...params} variant="outlined" />}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <InputLabel>Start Date</InputLabel>
                                <TextField type="date" fullWidth variant="outlined" value={filters.startDate} onChange={(e) => setFilters({ ...filters, startDate: e.target.value })} />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <InputLabel>End Date</InputLabel>
                                <TextField type="date" fullWidth variant="outlined" value={filters.endDate} onChange={(e) => setFilters({ ...filters, endDate: e.target.value })} />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <InputLabel>Litecard Member ID</InputLabel>
                                <TextField fullWidth variant="outlined" value={filters.litecard_member_id} onChange={(e) => setFilters({ ...filters, litecard_member_id: e.target.value })} />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <InputLabel>Email</InputLabel>
                                <TextField fullWidth variant="outlined" value={filters.email} onChange={(e) => setFilters({ ...filters, email: e.target.value })} />
                            </Grid>
                            <Grid item xs={12}>
                                <Box textAlign="right">
                                    <Button variant="contained" onClick={handleFilter}>
                                        Filter <ArrowForward sx={{ ml: 1 }} />
                                    </Button>
                                </Box>
                            </Grid>
                        </Grid>
                    </Box>
                </Card>
            </Collapse>

            {showActiveFilters && (
                <Box display="flex" flexWrap="wrap" gap={1} mt={2}>
                    {activeFilters.storeIds.length > 0 && activeFilters.storeIds.map(store => (
                        <Chip
                            key={store.id}
                            label={`Store: ${store.name}`}
                            onDelete={() => handleRemoveFilter('storeIds', store.id)}
                        />
                    ))}
                    {activeFilters.startDate && activeFilters.endDate && (
                        <Chip
                            label={`Date Range: ${activeFilters.startDate} to ${activeFilters.endDate}`}
                            onDelete={() => handleRemoveFilter('dateRange')}
                        />
                    )}
                    {activeFilters.litecard_member_id && (
                        <Chip
                            label={`Litecard Member ID: ${activeFilters.litecard_member_id}`}
                            onDelete={() => handleRemoveFilter('litecard_member_id')}
                        />
                    )}
                    {activeFilters.email && (
                        <Chip
                            label={`Email: ${activeFilters.email}`}
                            onDelete={() => handleRemoveFilter('email')}
                        />
                    )}
                </Box>
            )}

            {loading ? (
                <TableContainer component={Paper} sx={{ mt: 3 }}>
                    <Table>
                        <TableHead sx={{ backgroundColor: 'primary.main' }}>
                            <TableRow>
                                {sortableColumns.map((column) => (
                                    <TableCell key={column.id} sx={{ color: 'white' }}>
                                        <strong>{column.label}</strong>
                                    </TableCell>
                                ))}
                                <TableCell sx={{ color: 'white' }}><strong>View Details</strong></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {[...Array(5)].map((_, index) => (
                                <TableRow key={index}>
                                    <TableCell><Skeleton variant="text" width="80%" /></TableCell>
                                    <TableCell><Skeleton variant="text" width="60%" /></TableCell>
                                    <TableCell><Skeleton variant="text" width="50%" /></TableCell>
                                    <TableCell><Skeleton variant="text" width="70%" /></TableCell>
                                    <TableCell><Skeleton variant="text" width="90%" /></TableCell>
                                    <TableCell><Skeleton variant="text" width="60%" /></TableCell>
                                    <TableCell><Skeleton variant="rectangular" width={24} height={24} /></TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            ) : (
                <>
                    <TableContainer component={Paper} sx={{ mt: 3 }}>
                        <Table>
                            <TableHead sx={{ backgroundColor: 'primary.main' }}>
                                <TableRow>
                                    {sortableColumns.map((column) => (
                                        <TableCell key={column.id} sx={{ color: 'white' }}>
                                            <TableSortLabel
                                                active={currentColumn === column.field}
                                                direction={currentColumn === column.field ? (direction ? 'asc' : 'desc') : 'asc'}
                                                onClick={() => handleSort(column.field)}
                                                sx={{
                                                    color: 'white !important',
                                                    '& .MuiTableSortLabel-icon': {
                                                        color: 'white !important',
                                                    },
                                                }}
                                            >
                                                <strong>{column.label}</strong>
                                            </TableSortLabel>
                                        </TableCell>
                                    ))}
                                    <TableCell sx={{ color: 'white' }}><strong>View Details</strong></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.length > 0 ? data.map(item => (
                                    <TableRow key={item.sn}>
                                        <TableCell>{item.title}</TableCell>
                                        <TableCell>{item.store_id}</TableCell>
                                        <TableCell>{item.litecard_member_id}</TableCell>
                                        <TableCell>{item.fullname}</TableCell>
                                        <TableCell>{item.email}</TableCell>
                                        <TableCell>{item.last_sent}</TableCell>
                                        <TableCell>
                                            <IconButton onClick={() => handleViewClick(item)}>
                                                <Visibility />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                )) : (
                                    <TableRow>
                                        <TableCell colSpan={7} align="center">No data available</TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer>

                    <TablePagination
                        component="div"
                        count={pagination.total}
                        page={pagination.page}
                        onPageChange={(e, newPage) => {
                            setPagination(prev => ({ ...prev, page: newPage }));
                            const sortDirection = currentColumn ? (direction ? "asc" : "desc") : null;
                            fetchData(newPage, pagination.rowsPerPage, currentColumn, sortDirection);
                        }}
                        rowsPerPage={pagination.rowsPerPage}
                        onRowsPerPageChange={(e) => {
                            const newRows = parseInt(e.target.value, 10);
                            setPagination({ page: 0, rowsPerPage: newRows, total: pagination.total });
                            const sortDirection = currentColumn ? (direction ? "asc" : "desc") : null;
                            fetchData(0, newRows, currentColumn, sortDirection);
                        }}
                        rowsPerPageOptions={[20, 50, 100]}
                    />
                </>
            )}
            {/* Notification details modal */}
            <Modal
                open={modalOpen}
                onClose={handleModalClose}
                aria-labelledby="modal-title"
                aria-describedby="modal-description"
            >
                <Box
                    sx={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: { xs: "90%", sm: "70%", md: "50%" },
                        bgcolor: "background.paper",
                        boxShadow: 24,
                        borderRadius: 2,
                        overflow: "hidden",
                        maxHeight: "80vh",
                        overflowY: "auto",
                    }}
                >
                    {/* Header */}
                    <Box
                        sx={{
                            bgcolor: "primary.main",
                            color: "primary.contrastText",
                            px: 3,
                            py: 2,
                        }}
                    >
                        <Typography id="modal-title" variant="h6" fontWeight="bold">
                            Notification Details
                        </Typography>
                    </Box>

                    {/* Content */}
                    <Box sx={{ p: 3 }}>
                        {selectedRow && (
                            <Grid container spacing={2}>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Full Name
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.fullname || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Title
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.title || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Store
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.store_id || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        LiteCard Member ID
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.litecard_member_id || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Email
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.email || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Sent Date
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.last_sent || "-"}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Message Sent
                                    </Typography>
                                    <Typography fontWeight="medium">
                                        {selectedRow.message_sent || "-"}
                                    </Typography>
                                </Grid>

                            </Grid>
                        )}

                        {/* Footer */}
                        <Box sx={{ mt: 3, textAlign: "right" }}>
                            <Button onClick={handleModalClose} variant="contained">
                                Close
                            </Button>
                        </Box>
                    </Box>
                </Box>
            </Modal>

            <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={() => setOpen(false)}>
                <Alert onClose={() => setOpen(false)} severity={messageState} sx={{ width: '100%' }}>{message}</Alert>
            </Snackbar>
        </Box>
    );
}
