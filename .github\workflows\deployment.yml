name: deployment

on:
  push:
    branches:
      - ashish  # Replace with your specific branch name

jobs:
  build:
    runs-on: ubuntu-latest

    env:
      CI: false

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18' # Use the Node.js version compatible with your project

    - name: Install dependencies
      run: npm install --ignore-engines

    - name: Build the project
      run: npm run build

    - name: List Out Folders
      run: ls

    - name: Upload to FTP server
      uses: SamKirkland/FTP-Deploy-Action@4.3.0
      with:
        server: 101.0.85.42
        username: stagingsync
        password: "D2YwPp@3n9C8NpR!"
        local-dir: ./build/ # directory to upload
        server-dir: public_html/ # target directory on the FTP server
