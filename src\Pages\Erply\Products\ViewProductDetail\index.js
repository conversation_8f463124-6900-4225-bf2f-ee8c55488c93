import { Check, Clear, Close, Download, KeyboardArrowLeft, Sync, Visibility } from "@mui/icons-material";
import {
    AppBar,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Skeleton,
    styled,
    useTheme,
    Snackbar
} from "@mui/material";
import moment from "moment";
import React, { useEffect, useState } from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import PropTypes from "prop-types";
//import BasicTable from "../../../../Components/BasicTable";
import FsLightbox from "fslightbox-react";

import MuiAlert from "@mui/material/Alert";
//import ViewPolicyDialog from "../../price_policy/ViewPolicyDialog";
import parse from "html-react-parser";
import BasicTable from "../../../../Components/BasicTable";


const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const StyledHeaderTitle = styled(DialogTitle)(({ theme }) => ({
    background: theme.palette.primary.main,
    color: "#fff",
    position: "relative",
    "& button": {
        position: "absolute",
        right: "15px",
        top: "15px",
        color: "#fff",
    },
}));

const FlexContent = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
    marginBottom: "10px",
    alignItems: "flex-start",
}));

const FlexInnerTitle = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "249px",
    maxWidth: "250px",
    fontWeight: "600",
}));

const FlexContent2 = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "row",
    fontSize: "17px",
}));

const FlexInnerTitle2 = styled("div")(({ theme }) => ({
    display: "flex",
    fontWeight: "600",
    gap: "5px",
    marginRight: "5px",
}));

const BoxDiv = styled("div")(({ theme }) => ({
    textAlign: "center",
}));

const Values = styled("div")(({ theme }) => ({
    marginLeft: "10px",
    fontWeight: "500",
    color: theme.palette.primary.dark,
}));

const ImageDiv = styled("div")(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    width: "100%",
    flexWrap: "wrap",
    marginBottom: "10px",
}));

const ImageCell = styled("div")(({ theme }) => ({
    margin: "10px",
    width: "280px",
    borderRadius: "5px",
    overflow: "hidden",
    "& img": {
        width: "250px",
        height: "250px",
        objectFit: "cover",
        transition: "0.5s",
        boxShadow: theme.palette.primary.shadow,
        marginBottom: "10px",
        overflow: "hidden",
    },
    "& img:hover": {
        transform: "scale(1.1)",
    },
}));

const price_policyBox = styled(Box)(({ theme }) => ({
    display: "flex",
    marginBottom: "15px",
    "& h5": {
        margin: "5px",
    },
}));

const AppBarTabs = styled(AppBar)(({ theme }) => ({
    background: "#fff",
    color: theme.palette.primary.dark,
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

const variantsColumns = [
    //{ id: "checkColumn", name: " " },
    { id: "productID", name: "ERPLY Product ID" },
    { id: "code", name: "Product Code" },
    { id: "name", name: "Product Name" },
    { id: "type", name: "Product Type" },
    { id: "brandName", name: "Brand" },
    { id: "categoryName", name: "Category" },
    { id: "groupName", name: "Group" },
    { id: "priceWithVat", name: "Price(with VAT)" },
    { id: "status", name: "Status" },
    { id: "viewSoh", name: "" }

];

const sohColumns = [
    //{ id: "checkColumn", name: " " },
    { id: "erplyWarehouseID", name: "Warehouse ID" },
    { id: "warehouse_details", name: "Warehouse Name" },
    { id: "reservedStock", name: "Reserved Stocks" },
    { id: "totalStock", name: "Total Stocks" },
    { id: "erplyCurrentStockValue", name: "Available Stocks" },
];



const ViewProductDetail = (props) => {
    const theme = useTheme();
    const [value, setValue] = useState(0);
    const [open, setOpen] = useState(false);
    const [message, setMessage] = useState("");
    const [messageState, setMessageState] = useState("");
    const [togglerLanding, setTogglerLanding] = useState(false);
    const [imgIndex1, setImgIndex1] = useState(0);
    const [sohDetails, setSohDetails] = useState("");

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };


    const [dialogDetails, setDialogDetails] = useState({
        open: true,
    });

    useEffect(() => {
        props.sendDetails(dialogDetails);
    }, [props, dialogDetails]);

    const handleImageTogglerLanding = (index) => {
        setImgIndex1(index);
        setTogglerLanding((prev) => !prev);
    };

    const handleClose = () => {
        setDialogDetails({
            ...dialogDetails,
            open: false,
        });
    };

    const displayText = (descriptionTexts) => {
        const textIsEmpty = descriptionTexts === null || descriptionTexts === "";
        return !textIsEmpty ? (
            parse(descriptionTexts)
        ) : (
            "-"
        );
    }

    const handleView = (row) => {
        setSohDetails(row);
    };

    const handleBack = () => {
        setSohDetails("");
    }

    const handleCloseSnack = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                open={dialogDetails.open}
                onClose={handleClose}
                maxWidth="xl"
                fullWidth
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <StyledHeaderTitle id="alert-dialog-title">
                    <div>
                        View Product Details{" "}
                        {"(" +
                            //   (props.viewDetails.handle || "-") +
                            //   "/" +
                            (props.viewDetails.name || "-") +
                            ")"}
                    </div>
                    <IconButton onClick={handleClose}>
                        <Close />
                    </IconButton>
                </StyledHeaderTitle>
                {props.singleLoading ? (
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box p={3} sx={{ width: "100%" }}>
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                    <Skeleton height={40} />
                                </Box>
                            </Grid>
                        </Grid>
                    </DialogContent>
                ) : (
                    <DialogContent sx={{ padding: "0" }}>
                        <AppBarTabs position="static">
                            <Tabs
                                value={value}
                                onChange={handleChange}
                                indicatorColor="secondary"
                                textColor="inherit"
                                variant="fullWidth"
                                aria-label="full width tabs example"
                            >
                                <Tab label="Details" {...a11yProps(0)} />

                                <Tab label="Variants" {...a11yProps(1)} />

                            </Tabs>
                        </AppBarTabs>

                        <TabPanel value={value} index={0} dir={theme.direction}>
                            <Box>
                                <Grid container spacing={2}>
                                    {/* Left Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>ERPLY Product ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.productID || "-"}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>External ID</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.externalID || "-"}</Values>
                                        </FlexContent>
                                        
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Name</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.name || "-"}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Code</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.code || "-"}</Values>
                                        </FlexContent>


                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Type</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.type || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Category</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.categoryName || "-"}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Product Group</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.groupName || "-"}</Values>
                                        </FlexContent>
                                    </Grid>

                                    {/* Left Side */}

                                    {/* Right Side */}
                                    <Grid item xs={12} md={6}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Price(with VAT)</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                ${parseFloat(props.viewDetails.priceWithVat).toFixed(2)}
                                            </Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Pending Process</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.pendingProcess === 1 ? <Check color="primary" /> : <Clear color="error" />}</Values>
                                        </FlexContent>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Display In WebShop</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>{props.viewDetails.displayedInWebshop === 1 ? <Check color="primary" /> : <Clear color="error" />}</Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Status</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {props.viewDetails.status}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Created Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {moment(props.viewDetails.created_at).format(
                                                    "ddd, DD MMM YYYY, h:mm a"
                                                ) || "-"}
                                            </Values>
                                        </FlexContent>

                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Last Modified Date</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {moment(props.viewDetails.lastModified).format(
                                                    "ddd, DD MMM YYYY, h:mm a"
                                                ) || "-"}
                                            </Values>
                                        </FlexContent>
                                    </Grid>
                                    {/* Right Side */}

                                    <Grid item xs={12}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Long Description</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {/* {props.viewDetails.shortDescription || "-"} */}
                                                {displayText(props.viewDetails.description)}
                                            </Values>
                                        </FlexContent>
                                    </Grid>

                                    <Grid item xs={12}>
                                        <FlexContent>
                                            <FlexInnerTitle>
                                                <span>Sales Text</span> <span> : </span>
                                            </FlexInnerTitle>
                                            <Values>
                                                {/* {props.viewDetails.longDescription || "-"} */}
                                                {displayText(props.viewDetails.longdesc)}
                                            </Values>
                                        </FlexContent>
                                    </Grid>
                                </Grid>
                            </Box>
                        </TabPanel>

                        {/* <TabPanel value={value} index={1} dir={theme.direction}>

                            <Box>
                                {props.viewDetails.shopify_product_images && props.viewDetails.shopify_product_images.length > 0 ? (
                                    <>
                                        <h3>Shopify Images</h3>
                                        <ImageDiv>
                                            {props.viewDetails.shopify_product_images?.map((imgs, index) => (
                                                <ImageCell key={index} onClick={() => handleImageTogglerLanding(index)}>
                                                    <img src={imgs.src} alt="landing_image" />
                                                    <Box textAlign="center">
                                                        {imgs.name
                                                            .split("/")
                                                            .pop()
                                                            .replaceAll(".jpg", "")
                                                            .replaceAll(".png", "")}
                                                    </Box>
                                                </ImageCell>
                                            ))}
                                            <FsLightbox
                                                toggler={togglerLanding}
                                                sources={props.viewDetails.shopify_product_images?.map(img => img.src)}
                                                sourceIndex={imgIndex1}
                                                type="image"
                                                types={[
                                                    ...new Array(props.viewDetails.shopify_product_images?.length).fill("image"),
                                                ]}
                                            />
                                        </ImageDiv>
                                    </>
                                ) : (
                                    <h4>Images not available</h4>
                                )}
                            </Box>

                        </TabPanel>*/}



                        <TabPanel value={value} index={1} dir={theme.direction}>
                            {sohDetails ? (
                                <>
                                    <Box display={"flex"} justifyContent={"space-between"}>
                                        <h3>SOH Details</h3>
                                        <Button onClick={handleBack}>
                                            <KeyboardArrowLeft fontSize="small" sx={{ marginRight: "5px" }} />
                                            <span>Back</span>
                                        </Button>
                                    </Box>
                                   
                                    <BasicTable
                                        columns={sohColumns}
                                        rows={sohDetails.soh}
                                    />
                                </>
                            ) : (
                                <BasicTable
                                    columns={variantsColumns}
                                    rows={props.viewDetails.erply_product_variants}
                                    handleView={handleView}
                                />
                            )}
                        </TabPanel>
                    </DialogContent>
                )}
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined" color="primary">
                        Close
                    </Button>
                </DialogActions>
            </Dialog>

            {/* {openPolicyDialog && (
        <ViewPolicyDialog
          singleLoading={singleLoading}
          viewDetails={viewDetails}
          sendDetails={sendDetails}
        />
      )} */}

            <Snackbar
                autoHideDuration={3000}
                anchorOrigin={{ vertical: "top", horizontal: "right" }}
                open={open}
                onClose={handleCloseSnack}
            >
                <Alert
                    onClose={handleCloseSnack}
                    severity={messageState}
                    sx={{ width: "100%" }}
                >
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
};

export default ViewProductDetail;
