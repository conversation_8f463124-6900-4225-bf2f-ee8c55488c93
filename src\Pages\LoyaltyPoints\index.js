import React, { useState } from 'react';
import { Box, Button, Card, CardContent, Grid, Snackbar, TextField, Typography } from '@mui/material';
import httpclient from '../../Utils';
import MuiAlert from "@mui/material/Alert";
import { useNavigate } from 'react-router-dom';

const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

export default function LoyalityPoints() {
    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [apiError, setApiError] = useState('');
    const [validationError, setValidationError] = useState('');
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        fullName: '',
        email: '',
        mobile: '',
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
        // Clear validation error when user starts typing
        if (validationError) {
            setValidationError('');
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validation: Check if at least one field is filled
        if (!formData.fullName && !formData.email && !formData.mobile) {
            setValidationError('Please fill in at least one field: Full Name, Email, or Mobile.');
            return;
        }

        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (formData.fullName) params.append('full_name', formData.fullName);
            if (formData.email) params.append('email', formData.email);
            if (formData.mobile) params.append('mobile', formData.mobile);
            console.log(params);
            const response = await httpclient.get(`request-response?requestName=lightspeed/check-customer&${params.toString()}`);
            if (response.status === 200) {
                setLoading(false);
                navigate('/customer-details', { state: { customerData: response.data.data } });
            }
        } catch (err) {
            console.error(err);
            setApiError(err?.response?.data?.message || 'Error fetching Customer Data');
            setOpen(true);
            setLoading(false);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
            }}
        >
            <Card
                sx={{
                    width: 'md',
                    p: 3,
                    boxShadow: 3,
                    borderRadius: 2,
                }}
            >
                <CardContent>
                    <Typography variant="h6" align="center" fontWeight="bold" gutterBottom>
                        Search Customer by the Following Parameters
                    </Typography>

                    <form onSubmit={handleSubmit}>
                        <Grid container spacing={2} mt={1}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Full Name"
                                    placeholder="Enter full name"
                                    variant="outlined"
                                    name="fullName"
                                    value={formData.fullName}
                                    onChange={handleChange}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Email"
                                    placeholder="Enter valid email address"
                                    variant="outlined"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Mobile"
                                    placeholder="Enter valid mobile number"
                                    variant="outlined"
                                    name="mobile"
                                    value={formData.mobile}
                                    onChange={handleChange}
                                />
                            </Grid>

                            {validationError && (
                                <Grid item xs={12}>
                                    <Typography color="error" variant="body2" align="center">
                                        {validationError}
                                    </Typography>
                                </Grid>
                            )}

                            <Grid item xs={12}>
                                <Button
                                    type="submit"
                                    fullWidth
                                    variant="contained"
                                    sx={{
                                        bgcolor: 'primary',
                                        textTransform: 'none',
                                        fontWeight: 'bold',
                                    }}
                                    disabled={loading}
                                >
                                    {loading ? 'Searching...' : 'Search'}
                                </Button>
                            </Grid>
                        </Grid>
                    </form>
                </CardContent>
            </Card>

            <Snackbar autoHideDuration={3000} anchorOrigin={{ vertical: "top", horizontal: "right" }} open={open} onClose={() => setOpen(false)}>
                <Alert onClose={() => setOpen(false)} severity="error" sx={{ width: '100%' }}>{apiError}</Alert>
            </Snackbar>
        </Box>
    );
}